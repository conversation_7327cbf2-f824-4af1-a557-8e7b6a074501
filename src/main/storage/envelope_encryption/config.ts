/**
 * 🔧 客户端配置
 */

interface EncryptionConfig {
  algorithm: string
  keyLength: number
  ivLength: number
  tagLength: number
}

interface TimeoutConfig {
  apiRequest: number
  keyExpiry: number
}

interface StorageConfig {
  keyPrefix: string
  sessionPrefix: string
  maxRetries: number
  provider?: string
}

interface SecurityConfig {
  clearMemoryOnCleanup: boolean
  validateKeyExpiry: boolean
  enforceHttps: boolean
}

interface LoggingConfig {
  enabled: boolean
  level: string
}

interface Config {
  serverUrl: string
  encryption: EncryptionConfig
  timeout: TimeoutConfig
  storage: StorageConfig
  security: SecurityConfig
  logging: LoggingConfig
}

const config: Config = {
  // 🌐 服务端API地址
  serverUrl: process.env.KMS_SERVER_URL || 'http://localhost:3000/kms',

  // 🔐 加密配置
  encryption: {
    algorithm: 'aes-256-gcm',
    keyLength: 32, // 256位
    ivLength: 16, // 128位
    tagLength: 16 // 128位
  },

  // 🕐 超时配置
  timeout: {
    apiRequest: 10000, // API请求超时 10秒
    keyExpiry: 24 * 60 * 60 * 1000 // 密钥过期时间 24小时
  },

  // 💾 存储配置
  storage: {
    keyPrefix: 'kms_encrypted_key_',
    sessionPrefix: 'kms_session_',
    maxRetries: 3
  },

  // 🔒 安全配置
  security: {
    clearMemoryOnCleanup: true,
    validateKeyExpiry: true,
    enforceHttps: process.env.NODE_ENV === 'production'
  },

  // 📊 日志配置
  logging: {
    enabled: process.env.NODE_ENV !== 'production',
    level: 'info' // debug, info, warn, error
  }
}

// 🌍 环境特定配置 - Electron 主进程环境
// 在 Electron 主进程中，使用文件存储确保数据持久化
config.storage.provider = 'file'

export default config
export { Config, EncryptionConfig, TimeoutConfig, StorageConfig, SecurityConfig, LoggingConfig }
