import config from '../config'
import TempFileStorageProvider from './tempFileStorage'

interface EncryptedKeyData {
  encryptedDataKey: string
  encryptionContext: any
  timestamp: number
}

/**
 * 💾 客户端存储管理器
 *
 * 安全原则：
 * 1. 只存储加密后的数据密钥
 * 2. 支持多种存储后端
 * 3. 自动过期清理
 * 4. 安全删除
 * 5. 安全存储认证Token
 */
class StorageManager {
  private provider: any

  constructor() {
    this.provider = this.initializeProvider()
  }

  /**
   * 初始化存储提供者
   * 在 Electron 主进程中，始终使用文件存储确保数据持久化
   */
  private initializeProvider(): any {
    // Electron 主进程环境，使用文件存储
    console.log('🗂️ 初始化文件存储提供者（Electron 主进程）')
    return new TempFileStorageProvider()
  }

  /**
   * 🔑 存储认证Token
   * @param token - JWT Token
   */
  async storeAuthToken(token: string): Promise<void> {
    const key = `${config.storage.keyPrefix}auth_token`
    await this.provider.setItem(key, token)
    console.log('🔑 认证Token已存储')
  }

  /**
   * 🔑 获取认证Token
   * @returns JWT Token
   */
  async getAuthToken(): Promise<string | null> {
    const key = `${config.storage.keyPrefix}auth_token`
    return await this.provider.getItem(key)
  }

  /**
   * 🗑️ 清除认证Token
   */
  async clearAuthToken(): Promise<void> {
    const key = `${config.storage.keyPrefix}auth_token`
    await this.provider.removeItem(key)
    console.log('🗑️ 认证Token已清除')
  }

  /**
   * 💾 存储加密的数据密钥
   * @param userId - 用户ID
   * @param encryptedDataKey - 加密的数据密钥
   * @param encryptionContext - 加密上下文
   */
  async storeEncryptedDataKey(userId: string, encryptedDataKey: string, encryptionContext: any): Promise<void> {
    const key = `${config.storage.keyPrefix}${userId}`
    const data: EncryptedKeyData = {
      encryptedDataKey,
      encryptionContext,
      timestamp: Date.now()
    }

    await this.provider.setItem(key, JSON.stringify(data))
    console.log(`💾 用户 ${userId} 的加密数据密钥已存储`)
  }

  /**
   * 🔑 获取加密的数据密钥
   * @param userId - 用户ID
   * @returns 加密的数据密钥数据
   */
  async getEncryptedDataKey(userId: string): Promise<EncryptedKeyData | null> {
    const key = `${config.storage.keyPrefix}${userId}`
    const data = await this.provider.getItem(key)

    if (!data) {
      return null
    }

    try {
      const parsedData = JSON.parse(data)

      // 检查是否过期
      if (this.isExpired(parsedData.timestamp)) {
        console.log(`⏰ 用户 ${userId} 的数据密钥已过期，自动清理`)
        await this.clearEncryptedDataKey(userId)
        return null
      }

      console.log(`🔑 获取用户 ${userId} 的加密数据密钥`)
      return parsedData
    } catch (error) {
      console.error('❌ 解析存储的数据密钥失败:', error)
      return null
    }
  }

  /**
   * 🗑️ 清除加密的数据密钥
   * @param userId - 用户ID
   */
  async clearEncryptedDataKey(userId: string): Promise<void> {
    const key = `${config.storage.keyPrefix}${userId}`
    await this.provider.removeItem(key)
    console.log(`🗑️ 用户 ${userId} 的加密数据密钥已清除`)
  }

  /**
   * 💾 存储会话信息
   * @param userId - 用户ID
   * @param sessionId - 会话ID
   */
  async storeSession(userId: string, sessionId: string): Promise<void> {
    const key = `${config.storage.sessionPrefix}${userId}`
    await this.provider.setItem(key, sessionId)
    console.log(`💾 用户 ${userId} 的会话信息已存储`)
  }

  /**
   * 🔄 获取会话信息
   * @param userId - 用户ID
   * @returns 会话ID
   */
  async getSession(userId: string): Promise<string | null> {
    const key = `${config.storage.sessionPrefix}${userId}`
    return await this.provider.getItem(key)
  }

  /**
   * 🗑️ 清除会话信息
   * @param userId - 用户ID
   */
  async clearSession(userId: string): Promise<void> {
    const key = `${config.storage.sessionPrefix}${userId}`
    await this.provider.removeItem(key)
    console.log(`🗑️ 用户 ${userId} 的会话信息已清除`)
  }

  /**
   * 🗑️ 清除所有存储的数据
   */
  async clearAll(): Promise<void> {
    await this.provider.clear()
    console.log('🗑️ 所有存储数据已清除')
  }

  /**
   * 📊 获取存储统计信息
   * @returns 存储统计
   */
  async getStats(): Promise<any> {
    return await this.provider.getStats()
  }

  /**
   * 🧹 清理过期数据
   */
  async cleanupExpired(): Promise<void> {
    try {
      const stats = await this.getStats()
      const keys = stats.keys || []

      for (const key of keys) {
        if (key.startsWith(config.storage.keyPrefix)) {
          const data = await this.provider.getItem(key)
          if (data) {
            try {
              const parsedData = JSON.parse(data)
              if (this.isExpired(parsedData.timestamp)) {
                await this.provider.removeItem(key)
                console.log(`🧹 清理过期数据: ${key}`)
              }
            } catch (error) {
              // 忽略解析错误，可能是其他格式的数据
            }
          }
        }
      }
    } catch (error) {
      console.error('❌ 清理过期数据失败:', error)
    }
  }

  /**
   * ⏰ 检查数据是否过期
   * @param timestamp - 时间戳
   * @returns 是否过期
   */
  private isExpired(timestamp: number): boolean {
    const now = Date.now()
    const expiry = timestamp + config.timeout.keyExpiry
    return now > expiry
  }

  /**
   * 🔍 检查密钥是否存在
   * @param userId - 用户ID
   * @returns 密钥是否存在
   */
  async hasEncryptedDataKey(userId: string): Promise<boolean> {
    const data = await this.getEncryptedDataKey(userId)
    return data !== null
  }

  /**
   * 📝 列出所有用户的密钥
   * @returns 用户ID列表
   */
  async listUsers(): Promise<string[]> {
    try {
      const stats = await this.getStats()
      const keys = stats.keys || []
      const users: string[] = []

      for (const key of keys) {
        if (key.startsWith(config.storage.keyPrefix)) {
          const userId = key.replace(config.storage.keyPrefix, '')
          if (userId && !userId.includes('auth_token')) {
            users.push(userId)
          }
        }
      }

      return users
    } catch (error) {
      console.error('❌ 列出用户失败:', error)
      return []
    }
  }

  /**
   * 🔄 迁移旧格式数据（如果需要）
   */
  async migrateData(): Promise<void> {
    try {
      console.log('🔄 检查是否需要数据迁移...')
      // 这里可以添加数据迁移逻辑
      console.log('✅ 数据迁移检查完成')
    } catch (error) {
      console.error('❌ 数据迁移失败:', error)
    }
  }
}

// 导出便捷函数
async function storeAuthToken(token: string): Promise<void> {
  const storage = new StorageManager()
  await storage.storeAuthToken(token)
}

async function getAuthToken(): Promise<string | null> {
  const storage = new StorageManager()
  return await storage.getAuthToken()
}

async function clearAuthToken(): Promise<void> {
  const storage = new StorageManager()
  await storage.clearAuthToken()
}

export default StorageManager
export { StorageManager, storeAuthToken, getAuthToken, clearAuthToken }
export type { EncryptedKeyData }
