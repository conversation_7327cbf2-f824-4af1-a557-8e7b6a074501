/**
 * 🔗 Chaterm 认证适配器
 * 
 * 用于桥接 envelope_encryption 和 Chaterm 项目的认证系统
 * 在主进程中安全地获取用户认证信息
 */

import { ipcMain } from 'electron'

interface TokenData {
  token: string
  userId: string
  expiry?: number
}

interface AuthStatus {
  hasToken: boolean
  hasUserId: boolean
  isValid: boolean
  tokenType: 'guest' | 'user'
  expiry: number | null
}

class ChatermAuthAdapter {
  private cachedToken: string | null = null
  private cachedUserId: string | null = null
  private tokenExpiry: number | null = null

  constructor() {
    this.setupIPCHandlers()
  }

  /**
   * 设置 IPC 处理器，用于从渲染进程获取认证信息
   */
  private setupIPCHandlers(): void {
    // 处理来自渲染进程的 token 更新
    ipcMain.handle('auth:update-token', (event, tokenData: TokenData) => {
      this.cachedToken = tokenData.token
      this.cachedUserId = tokenData.userId
      this.tokenExpiry = tokenData.expiry || (Date.now() + 24 * 60 * 60 * 1000) // 默认24小时过期
      console.log('🔑 主进程已更新缓存的认证信息')
      return { success: true }
    })

    // 处理清除认证信息
    ipcMain.handle('auth:clear-token', () => {
      this.cachedToken = null
      this.cachedUserId = null
      this.tokenExpiry = null
      console.log('🗑️ 主进程已清除缓存的认证信息')
      return { success: true }
    })
  }

  /**
   * 获取当前用户的 JWT Token
   * @returns JWT Token
   */
  async getAuthToken(): Promise<string | null> {
    try {
      // 检查缓存的 token 是否有效
      if (this.cachedToken && this.isTokenValid()) {
        console.log('🔑 使用缓存的 JWT Token')
        return this.cachedToken
      }

      // 如果缓存无效，尝试从渲染进程请求最新的 token
      console.log('🔄 缓存的 Token 无效，尝试获取最新 Token')
      
      // 这里可以通过 webContents 向渲染进程请求最新的 token
      // 但为了简化，我们先返回缓存的 token（即使可能过期）
      if (this.cachedToken) {
        console.log('⚠️ 使用可能过期的缓存 Token')
        return this.cachedToken
      }

      console.log('❌ 没有可用的 JWT Token')
      return null
    } catch (error) {
      console.error('❌ 获取 JWT Token 失败:', error)
      return null
    }
  }

  /**
   * 获取当前用户ID
   * @returns 用户ID
   */
  async getCurrentUserId(): Promise<string | null> {
    try {
      if (this.cachedUserId) {
        console.log('👤 使用缓存的用户ID:', this.cachedUserId)
        return this.cachedUserId
      }

      // 如果没有缓存的用户ID，返回默认的guest用户
      console.log('👤 使用默认的guest用户ID')
      return 'guest_user'
    } catch (error) {
      console.error('❌ 获取用户ID失败:', error)
      return 'guest_user'
    }
  }

  /**
   * 检查 token 是否有效
   * @returns token 是否有效
   */
  private isTokenValid(): boolean {
    if (!this.cachedToken || !this.tokenExpiry) {
      return false
    }

    // 检查是否为 guest token
    if (this.cachedToken === 'guest_token') {
      return true // guest token 不过期
    }

    // 检查是否过期（提前5分钟认为过期）
    const fiveMinutes = 5 * 60 * 1000
    return Date.now() < (this.tokenExpiry - fiveMinutes)
  }

  /**
   * 手动设置认证信息（用于初始化）
   * @param token JWT Token
   * @param userId 用户 ID
   * @param expiry 过期时间戳
   */
  setAuthInfo(token: string, userId: string, expiry: number | null = null): void {
    this.cachedToken = token
    this.cachedUserId = userId
    this.tokenExpiry = expiry || (Date.now() + 24 * 60 * 60 * 1000)
    console.log('🔑 手动设置认证信息完成')
  }

  /**
   * 清除认证信息
   */
  clearAuthInfo(): void {
    this.cachedToken = null
    this.cachedUserId = null
    this.tokenExpiry = null
    console.log('🗑️ 已清除认证信息')
  }

  /**
   * 获取认证状态
   * @returns 认证状态信息
   */
  getAuthStatus(): AuthStatus {
    return {
      hasToken: !!this.cachedToken,
      hasUserId: !!this.cachedUserId,
      isValid: this.isTokenValid(),
      tokenType: this.cachedToken === 'guest_token' ? 'guest' : 'user',
      expiry: this.tokenExpiry
    }
  }
}

// 创建单例实例
const chatermAuthAdapter = new ChatermAuthAdapter()

export { ChatermAuthAdapter, chatermAuthAdapter }
export type { TokenData, AuthStatus }
