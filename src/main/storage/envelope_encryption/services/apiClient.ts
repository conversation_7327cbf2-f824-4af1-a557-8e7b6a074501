import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import config from '../config'
import { getAuthToken, clearAuthToken } from '../utils/storage'

interface GenerateDataKeyRequest {
  encryptionContext: any
  authToken?: string | null
}

interface GenerateDataKeyResponse {
  success: boolean
  dataKey?: string
  encryptedDataKey?: string
  keyFingerprint?: string
  error?: string
}

interface DecryptDataKeyRequest {
  encryptedDataKey: string
  encryptionContext: any
  authToken?: string | null
}

interface DecryptDataKeyResponse {
  success: boolean
  dataKey?: string
  keyFingerprint?: string
  error?: string
}

/**
 * 🌐 API客户端 - 使用axios与KMS服务端通信
 * 
 * 安全原则：
 * 1. 使用axios拦截器统一处理认证Token
 * 2. 自动处理401未授权错误
 * 3. 请求超时保护
 */
class ApiClient {
  private client: AxiosInstance
  private serverUrl: string

  constructor(serverUrl?: string) {
    this.serverUrl = serverUrl || config.serverUrl
    
    // 创建axios实例
    this.client = axios.create({
      baseURL: this.serverUrl,
      timeout: config.timeout.apiRequest,
    })

    this.setupInterceptors()
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器：自动附加Authorization头
    this.client.interceptors.request.use(
      async (config: AxiosRequestConfig) => {
        const token = await getAuthToken()
        if (token) {
          config.headers = config.headers || {}
          config.headers['Authorization'] = `Bearer ${token}`
          console.log('🔑 请求已附带Token')
        }
        return config
      },
      (error) => {
        console.error('❌ 请求拦截器错误:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器：处理全局错误，特别是401
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        return response.data
      },
      async (error) => {
        if (error.response && error.response.status === 401) {
          console.warn('⚠️ 认证失败 (401)，清除本地Token')
          await clearAuthToken()
        }
        const errorMessage = error.response?.data?.error || error.message
        return Promise.reject(new Error(errorMessage))
      }
    )
  }

  /**
   * 🔑 生成数据密钥
   * @param request - 生成数据密钥请求
   * @returns 生成数据密钥响应
   */
  async generateDataKey(request: GenerateDataKeyRequest): Promise<GenerateDataKeyResponse> {
    try {
      console.log('🔑 请求生成数据密钥...')
      
      const requestData = {
        encryptionContext: request.encryptionContext
      }

      // 如果提供了authToken，使用它覆盖默认的token
      const headers: any = {}
      if (request.authToken) {
        headers['Authorization'] = request.authToken.startsWith('Bearer ') 
          ? request.authToken 
          : `Bearer ${request.authToken}`
      }

      const response = await this.client.post('/api/kms/generate-data-key', requestData, {
        headers: Object.keys(headers).length > 0 ? headers : undefined
      })

      console.log('✅ 数据密钥生成成功')
      return response
    } catch (error) {
      console.error('❌ 生成数据密钥失败:', error)
      return {
        success: false,
        error: (error as Error).message
      }
    }
  }

  /**
   * 🔓 解密数据密钥
   * @param request - 解密数据密钥请求
   * @returns 解密数据密钥响应
   */
  async decryptDataKey(request: DecryptDataKeyRequest): Promise<DecryptDataKeyResponse> {
    try {
      console.log('🔓 请求解密数据密钥...')
      
      const requestData = {
        encryptedDataKey: request.encryptedDataKey,
        encryptionContext: request.encryptionContext
      }

      // 如果提供了authToken，使用它覆盖默认的token
      const headers: any = {}
      if (request.authToken) {
        headers['Authorization'] = request.authToken.startsWith('Bearer ') 
          ? request.authToken 
          : `Bearer ${request.authToken}`
      }

      const response = await this.client.post('/api/kms/decrypt-data-key', requestData, {
        headers: Object.keys(headers).length > 0 ? headers : undefined
      })

      console.log('✅ 数据密钥解密成功')
      return response
    } catch (error) {
      console.error('❌ 解密数据密钥失败:', error)
      return {
        success: false,
        error: (error as Error).message
      }
    }
  }

  /**
   * 🏥 健康检查
   * @returns 健康状态
   */
  async healthCheck(): Promise<any> {
    try {
      console.log('🏥 执行健康检查...')
      const response = await this.client.get('/api/health')
      console.log('✅ 健康检查通过')
      return response
    } catch (error) {
      console.error('❌ 健康检查失败:', error)
      throw error
    }
  }

  /**
   * 🔄 轮换主密钥
   * @returns 轮换结果
   */
  async rotateMasterKey(): Promise<any> {
    try {
      console.log('🔄 请求轮换主密钥...')
      const response = await this.client.post('/api/kms/rotate-master-key')
      console.log('✅ 主密钥轮换成功')
      return response
    } catch (error) {
      console.error('❌ 主密钥轮换失败:', error)
      throw error
    }
  }

  /**
   * 📊 获取KMS统计信息
   * @returns 统计信息
   */
  async getStats(): Promise<any> {
    try {
      console.log('📊 获取KMS统计信息...')
      const response = await this.client.get('/api/kms/stats')
      console.log('✅ 获取统计信息成功')
      return response
    } catch (error) {
      console.error('❌ 获取统计信息失败:', error)
      throw error
    }
  }

  /**
   * 🔍 验证数据密钥
   * @param encryptedDataKey - 加密的数据密钥
   * @param encryptionContext - 加密上下文
   * @returns 验证结果
   */
  async validateDataKey(encryptedDataKey: string, encryptionContext: any): Promise<any> {
    try {
      console.log('🔍 验证数据密钥...')
      const response = await this.client.post('/api/kms/validate-data-key', {
        encryptedDataKey,
        encryptionContext
      })
      console.log('✅ 数据密钥验证成功')
      return response
    } catch (error) {
      console.error('❌ 数据密钥验证失败:', error)
      throw error
    }
  }

  /**
   * 🗑️ 撤销数据密钥
   * @param keyFingerprint - 密钥指纹
   * @returns 撤销结果
   */
  async revokeDataKey(keyFingerprint: string): Promise<any> {
    try {
      console.log('🗑️ 撤销数据密钥...')
      const response = await this.client.post('/api/kms/revoke-data-key', {
        keyFingerprint
      })
      console.log('✅ 数据密钥撤销成功')
      return response
    } catch (error) {
      console.error('❌ 数据密钥撤销失败:', error)
      throw error
    }
  }

  /**
   * 📝 记录审计日志
   * @param action - 操作类型
   * @param details - 操作详情
   * @returns 记录结果
   */
  async logAudit(action: string, details: any): Promise<any> {
    try {
      const response = await this.client.post('/api/kms/audit-log', {
        action,
        details,
        timestamp: Date.now()
      })
      return response
    } catch (error) {
      console.error('❌ 记录审计日志失败:', error)
      // 审计日志失败不应该影响主要功能
      return { success: false, error: (error as Error).message }
    }
  }

  /**
   * 🔧 更新服务器URL
   * @param newUrl - 新的服务器URL
   */
  updateServerUrl(newUrl: string): void {
    this.serverUrl = newUrl
    this.client.defaults.baseURL = newUrl
    console.log(`🔧 API服务器URL已更新为: ${newUrl}`)
  }

  /**
   * 📊 获取客户端状态
   * @returns 客户端状态
   */
  getStatus(): any {
    return {
      serverUrl: this.serverUrl,
      timeout: config.timeout.apiRequest,
      connected: true // 这里可以添加连接状态检查
    }
  }
}

export default ApiClient
export { ApiClient }
export type { 
  GenerateDataKeyRequest, 
  GenerateDataKeyResponse, 
  DecryptDataKeyRequest, 
  DecryptDataKeyResponse 
}
